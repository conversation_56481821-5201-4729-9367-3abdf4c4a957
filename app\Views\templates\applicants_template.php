<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="Description" content="Government Recruitment and Selection System" />
    <link rel="shortcut icon" href="<?= base_url() ?>/public/assets/system_img/favicon.ico" type="image/x-icon">
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Material Design Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <!-- Font Awesome - Local Installation -->
    <link rel="stylesheet" href="<?= base_url() ?>/public/assets/fontawesome/fontawesome-free-6.4.0-web/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- DataTables Bootstrap 5 -->
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <!-- Toastr CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" rel="stylesheet">
    <!-- Google Charts Library -->
    <script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>

    <title><?= isset($title) ? $title . ' - ' : '' ?>GRASS</title>

    <style>
        :root {
            --navy: #002B5B;
            --lime: #BFFF00;
            --dark-green: #4D8C57;
            --accent-red: #FF3366;
            --deep-red: #CC2952;
            --navy-light: #003B7B;
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: #f8f9fa;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .navbar {
            background: linear-gradient(135deg, var(--navy), var(--navy-light));
            padding: 1rem 0;
        }

        .navbar-brand img {
            height: 40px;
            width: auto;
        }

        .nav-link {
            color: rgba(255, 255, 255, 0.9) !important;
            padding: 0.5rem 1rem;
            transition: all 0.3s;
        }

        .nav-link:hover {
            color: var(--lime) !important;
        }

        .nav-link.active {
            color: var(--lime) !important;
            font-weight: 500;
        }

        .dropdown-menu {
            border: none;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }

        .dropdown-item:hover {
            background-color: rgba(255, 51, 102, 0.1);
            color: var(--accent-red);
        }

        .main-content {
            flex: 1;
            padding: 2rem 0;
        }

        .footer {
            background: linear-gradient(135deg, var(--navy), var(--navy-light));
            color: white;
            padding: 1.5rem 0;
            margin-top: auto;
        }

        /* Card Hover Effect */
        .hover-card {
            transition: all 0.3s;
        }

        .hover-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 .5rem 1rem rgba(255, 51, 102, 0.15);
        }

        /* Notification Badge */
        .notification-badge {
            background-color: var(--accent-red);
        }

        /* Custom Button Styles */
        .btn-primary {
            background-color: var(--accent-red);
            border-color: var(--accent-red);
        }

        .btn-primary:hover {
            background-color: var(--deep-red);
            border-color: var(--deep-red);
        }

        .btn-outline-primary {
            color: var(--accent-red);
            border-color: var(--accent-red);
        }

        .btn-outline-primary:hover {
            background-color: var(--accent-red);
            border-color: var(--accent-red);
        }
    </style>
</head>

<body>
    <?php if(session()->get('logged_in')): ?>
    <!-- Admin Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark sticky-top">
        <div class="container-fluid px-4">
            <a class="navbar-brand d-flex align-items-center" href="<?= base_url('dashboard') ?>">
                <img src="<?= base_url() ?>/public/assets/system_img/system-logo.png" alt="Logo" class="me-3">
                <span class="h4 mb-0">GRASS</span>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a href="<?= base_url('applicant/dashboard') ?>" class="nav-link <?= ($menu == 'dashboard') ? 'active' : '' ?>">
                            <i class="fas fa-home me-2"></i>Dashboard
                        </a>
                    </li>
                    
                    <li class="nav-item">
                        <a href="<?= base_url('applicant/jobs') ?>" class="nav-link <?= ($menu == 'jobs') ? 'active' : '' ?>">
                            <i class="fas fa-briefcase me-2"></i>Job Openings
                        </a>
                    </li>

                    <li class="nav-item">
                        <a href="<?= base_url('applicant/applications') ?>" class="nav-link <?= ($menu == 'applications') ? 'active' : '' ?>">
                            <i class="fas fa-file-alt me-2"></i>Applications
                        </a>
                    </li>

                    <li class="nav-item">
                        <a href="<?= base_url('applicant/profile') ?>" class="nav-link <?= ($menu == 'profile') ? 'active' : '' ?>">
                            <i class="fas fa-user me-2"></i>Profile
                        </a>
                    </li>

                    <li class="nav-item">
                        <a href="<?= base_url('logout') ?>" class="nav-link">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </a>
                    </li>
                </ul>

                <div class="d-flex align-items-center">
                    <!-- Notifications -->
                    <div class="position-relative me-3">
                        <button class="btn btn-link text-white position-relative">
                            <i class="fas fa-bell fs-5"></i>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill notification-badge">
                                3
                            </span>
                        </button>
                    </div>

                    <!-- Welcome Text -->
                    <span class="text-white">
                        Welcome, <?= session()->get('applicant_name') ?>
                    </span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container-fluid px-4">
            <?= $this->renderSection('content') ?>
        </div>
    </main>

    <?php else: ?>
    <!-- Public Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="<?= base_url() ?>">
                <img src="<?= base_url() ?>/public/assets/system_img/system-logo.png" alt="Logo" class="me-3">
                <span class="h4 mb-0">GRASS</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a href="<?= base_url() ?>" class="nav-link <?= ($menu == 'home') ? 'active' : '' ?>">Home</a>
                    </li>
                    <li class="nav-item">
                        <a href="<?= base_url('login') ?>" class="nav-link <?= ($menu == 'login') ? 'active' : '' ?>">Login</a>
                    </li>
                    <li class="nav-item">
                        <a href="<?= base_url('about') ?>" class="nav-link <?= ($menu == 'about') ? 'active' : '' ?>">About</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <?= $this->renderSection('content') ?>
    <?php endif; ?>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="text-center">
                <div class="mb-3">
                    <img src="<?= base_url() ?>/public/assets/system_img/dakoii-logo.png" alt="Dakoii" height="32">
                </div>
                <p class="small mb-1">&copy; 2024 <a href="https://www.dakoiims.com" class="text-lime text-decoration-none">Dakoii Systems</a></p>
                <p class="small mb-0"><?= SYSTEM_NAME ?> <?= SYSTEM_VERSION ?></p>
            </div>
        </div>
    </footer>

    <!-- Core JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- DataTables -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <!-- Toastr JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>

    <!-- Toastr Messages -->
    <script>
        // Configure Toastr options
        toastr.options = {
            "closeButton": true,
            "progressBar": true,
            "positionClass": "toast-top-right",
            "timeOut": "3000"
        };

        <?php if (session()->getFlashdata('success')): ?>
            toastr.success('<?= session()->getFlashdata('success') ?>');
        <?php endif; ?>

        <?php if (session()->getFlashdata('error')): ?>
            toastr.error('<?= session()->getFlashdata('error') ?>');
        <?php endif; ?>
    </script>

    <!-- Page specific scripts -->
    <?= $this->renderSection('scripts') ?>
</body>
</html>