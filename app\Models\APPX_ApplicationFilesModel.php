<?php

namespace App\Models;

use CodeIgniter\Model;

/**
 * APPX_ApplicationFilesModel
 * 
 * Model for the appx_application_files table
 */
class APPX_ApplicationFilesModel extends Model
{
    protected $table         = 'appx_application_files';
    protected $primaryKey    = 'id';
    protected $useAutoIncrement = true;
    protected $returnType    = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    
    // Fields that can be set during save, insert, update
    protected $allowedFields = [
        'application_id', 'applicant_id', 'file_title', 'file_description',
        'file_path', 'created_by', 'updated_by'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    
    // Validation
    protected $validationRules = [
        'application_id'  => 'required|numeric',
        'applicant_id'    => 'required|numeric',
        'file_title'      => 'required|max_length[255]',
        'file_path'       => 'required|max_length[255]'
    ];
    
    protected $validationMessages = [
        'application_id' => [
            'required' => 'Application ID is required',
            'numeric'  => 'Application ID must be a number'
        ],
        'applicant_id' => [
            'required' => 'Applicant ID is required',
            'numeric'  => 'Applicant ID must be a number'
        ],
        'file_title' => [
            'required'    => 'File title is required',
            'max_length'  => 'File title cannot exceed 255 characters'
        ],
        'file_path' => [
            'required'    => 'File path is required',
            'max_length'  => 'File path cannot exceed 255 characters'
        ]
    ];
    
    protected $skipValidation = false;
    protected $cleanValidationRules = true;
    
    /**
     * Get file by ID
     *
     * @param int $id
     * @return array|null
     */
    public function getFileById($id)
    {
        return $this->find($id);
    }
    
    /**
     * Get files by application ID
     *
     * @param int $applicationId
     * @return array
     */
    public function getFilesByApplicationId($applicationId)
    {
        return $this->where('application_id', $applicationId)->findAll();
    }
    
    /**
     * Get files by applicant ID
     *
     * @param int $applicantId
     * @return array
     */
    public function getFilesByApplicantId($applicantId)
    {
        return $this->where('applicant_id', $applicantId)->findAll();
    }
    
    /**
     * Get files by title (partial match)
     *
     * @param string $title
     * @return array
     */
    public function getFilesByTitle($title)
    {
        return $this->like('file_title', $title)->findAll();
    }
    
    /**
     * Delete file by ID
     *
     * @param int $id
     * @return bool
     */
    public function deleteFile($id)
    {
        // Get the file record first to potentially handle physical file deletion
        $file = $this->find($id);
        
        if ($file) {
            // Delete the database record
            return $this->delete($id);
        }
        
        return false;
    }
} 