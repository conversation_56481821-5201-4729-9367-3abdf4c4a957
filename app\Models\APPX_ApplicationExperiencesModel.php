<?php

namespace App\Models;

use CodeIgniter\Model;

/**
 * APPX_ApplicationExperiencesModel
 *
 * Model for the appx_application_experiences table
 * @property array $allowedFields List of fields that can be modified
 * @property bool $useTimestamps Whether to use timestamps
 */
class APPX_ApplicationExperiencesModel extends Model
{
    protected $table         = 'appx_application_experiences';
    protected $primaryKey    = 'id';
    protected $useAutoIncrement = true;
    protected $returnType    = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;

    // Fields that can be set during save, insert, update
    protected $allowedFields = [
        'application_id', 'applicant_id', 'employer', 'employer_contacts_address',
        'position', 'date_from', 'date_to', 'achievements', 'work_description',
        'created_by', 'updated_by'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Validation
    protected $validationRules = [
        'application_id'  => 'required|numeric',
        'applicant_id'    => 'required|numeric',
        'employer'        => 'required|max_length[255]',
        'position'        => 'required|max_length[255]',
        'date_from'       => 'required|valid_date'
    ];

    protected $validationMessages = [
        'application_id' => [
            'required' => 'Application ID is required',
            'numeric'  => 'Application ID must be a number'
        ],
        'applicant_id' => [
            'required' => 'Applicant ID is required',
            'numeric'  => 'Applicant ID must be a number'
        ],
        'employer' => [
            'required'    => 'Employer name is required',
            'max_length'  => 'Employer name cannot exceed 255 characters'
        ],
        'position' => [
            'required'    => 'Position is required',
            'max_length'  => 'Position cannot exceed 255 characters'
        ],
        'date_from' => [
            'required'    => 'Start date is required',
            'valid_date'  => 'Start date must be a valid date'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    /**
     * Initialize model with indexes
     */
    public function __construct()
    {
        parent::__construct();

        // Check if indexes exist using a safer method
        try {
            // Get all indexes for this table
            $query = $this->db->query("SHOW INDEX FROM {$this->table}");
            $indexes = $query->getResultArray();

            // Create arrays of existing index names
            $existingIndexes = [];
            foreach ($indexes as $index) {
                $existingIndexes[] = $index['Key_name'];
            }

            // Only add indexes if they don't already exist
            if (!in_array('application_id_idx', $existingIndexes)) {
                $this->db->query("ALTER TABLE {$this->table} ADD INDEX application_id_idx (application_id)");
            }

            if (!in_array('applicant_id_idx', $existingIndexes)) {
                $this->db->query("ALTER TABLE {$this->table} ADD INDEX applicant_id_idx (applicant_id)");
            }

            if (!in_array('date_from_idx', $existingIndexes)) {
                $this->db->query("ALTER TABLE {$this->table} ADD INDEX date_from_idx (date_from)");
            }
        } catch (\Exception $e) {
            // Log the error but don't stop execution
            log_message('error', 'Error checking or creating indexes: ' . $e->getMessage());
        }
    }

    /**
     * Get experience by ID with caching
     *
     * @param int $id
     * @return array|null
     */
    public function getExperienceById($id)
    {
        $cacheKey = "experience_{$id}";
        if ($cached = cache($cacheKey)) {
            return $cached;
        }

        $result = $this->find($id);
        cache()->save($cacheKey, $result, 300); // Cache for 5 minutes
        return $result;
    }

    /**
     * Get experiences by application ID with efficient ordering
     *
     * @param int $applicationId
     * @return array
     */
    public function getExperiencesByApplicationId($applicationId)
    {
        return $this->where('application_id', $applicationId)
                    ->orderBy('date_from', 'DESC')
                    ->findAll();
    }

    /**
     * Get experiences by applicant ID with efficient ordering
     *
     * @param int $applicantId
     * @return array
     */
    public function getExperiencesByApplicantId($applicantId)
    {
        return $this->where('applicant_id', $applicantId)
                    ->orderBy('date_from', 'DESC')
                    ->findAll();
    }

    /**
     * Get current or most recent experience using optimized query
     *
     * @param int $applicationId
     * @return array|null
     */
    public function getCurrentExperience($applicationId)
    {
        return $this->where('application_id', $applicationId)
                    ->groupStart()
                        ->where('date_to IS NULL')
                        ->orWhere('date_to >=', date('Y-m-d'))
                    ->groupEnd()
                    ->orderBy('date_from', 'DESC')
                    ->first();
    }

    /**
     * Get total years of experience using SQL calculation
     *
     * @param int $applicationId
     * @return float
     */
    public function getTotalYearsOfExperience($applicationId)
    {
        $result = $this->db->query("
            SELECT ROUND(
                SUM(
                    DATEDIFF(
                        COALESCE(date_to, CURDATE()),
                        date_from
                    ) / 365.25
                ),
                1
            ) as total_years
            FROM {$this->table}
            WHERE application_id = ?
        ", [$applicationId])->getRow();

        return $result->total_years ?? 0;
    }

    /**
     * Get experiences summary with efficient single query
     *
     * @param int $applicationId
     * @return array
     */
    public function getExperiencesSummary($applicationId)
    {
        return $this->select([
            'COUNT(*) as total_positions',
            'GROUP_CONCAT(DISTINCT employer) as employers',
            'MIN(date_from) as earliest_experience',
            'MAX(COALESCE(date_to, CURDATE())) as latest_experience',
            'ROUND(SUM(DATEDIFF(COALESCE(date_to, CURDATE()), date_from) / 365.25), 1) as total_years'
        ])
        ->where('application_id', $applicationId)
        ->get()
        ->getRow();
    }
}