<?php

namespace App\Models;

use CodeIgniter\Model;

/**
 * APPX_ApplicationInformationModel
 *
 * Model for the appx_application_information table
 */
class APPX_ApplicationInformationModel extends Model
{
    protected $table         = 'appx_application_information';
    protected $primaryKey    = 'id';
    protected $useAutoIncrement = true;
    protected $returnType    = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;

    // Fields that can be set during save, insert, update
    protected $allowedFields = [
        'org_id', 'exercise_id', 'applicant_id', 'position_id', 'application_number', 'fname', 'lname',
        'gender', 'dobirth', 'place_of_origin', 'id_photo_path', 'contact_details',
        'location_address', 'id_numbers', 'current_employer', 'current_position',
        'current_salary', 'citizenship', 'marital_status', 'date_of_marriage',
        'spouse_employer', 'children', 'offence_convicted', 'referees',
        'how_did_you_hear_about_us', 'signature_path', 'publications', 'awards',
        'files_extracted_texts',
        'recieved_acknowledged', 'recieved_by', 'recieved_at',
        'status', 'remarks', 'created_by', 'updated_by',
        'pre_screened', 'pre_screened_by', 'pre_screened_status', 'pre_screened_remarks',
        'pre_screened_criteria_results',
        'profile_status', 'profile_details', 'profiled_by', 'profiled_at',
        // Rating fields
        'rating_age', 'rating_age_max',
        'rating_qualification', 'rating_qualification_max',
        'rating_experience_private_non_relevant', 'rating_experience_private_non_relevant_max',
        'rating_experience_private_relevant', 'rating_experience_private_relevant_max',
        'rating_experience_public_non_relevant', 'rating_experience_public_non_relevant_max',
        'rating_experience_public_relevant', 'rating_experience_public_relevant_max',
        'rating_trainings', 'rating_trainings_max',
        'rating_skills_competencies', 'rating_skills_competencies_max',
        'rating_knowledge', 'rating_knowledge_max',
        'rating_public_service', 'rating_public_service_max',
        'rating_capability', 'rating_capability_max',
        'rating_remarks',
        'rating_status', 'rated_by', 'rated_at',
        'shortlisted', 'shortlisted_by', 'shortlisted_at',
        'interviewed', 'interviewed_by', 'interviewed_at', 'interview_rated',
        'pre_selection', 'pre_selection_by', 'preselection_at',
        'selected', 'selected_by', 'selected_at'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Validation
    protected $validationRules = [
        'org_id'             => 'required|numeric',
        'exercise_id'        => 'required|numeric',
        'applicant_id'       => 'required|numeric',
        'position_id'        => 'required|numeric',
        'application_number' => 'required|max_length[20]',
        'fname'              => 'required|max_length[255]',
        'lname'              => 'required|max_length[255]',
    ];

    protected $validationMessages = [
        'org_id' => [
            'required' => 'Organization ID is required',
            'numeric'  => 'Organization ID must be a number'
        ],
        'exercise_id' => [
            'required' => 'Exercise ID is required',
            'numeric'  => 'Exercise ID must be a number'
        ],
        'applicant_id' => [
            'required' => 'Applicant ID is required',
            'numeric'  => 'Applicant ID must be a number'
        ],
        'position_id' => [
            'required' => 'Position ID is required',
            'numeric'  => 'Position ID must be a number'
        ],
        'application_number' => [
            'required'    => 'Application number is required',
            'max_length'  => 'Application number cannot exceed 20 characters'
        ],
        'fname' => [
            'required'    => 'First name is required',
            'max_length'  => 'First name cannot exceed 255 characters'
        ],
        'lname' => [
            'required'    => 'Last name is required',
            'max_length'  => 'Last name cannot exceed 255 characters'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    /**
     * Get application by ID
     *
     * @param int $id
     * @return array|null
     */
    public function getApplicationById($id)
    {
        return $this->find($id);
    }

    /**
     * Get applications by applicant ID
     *
     * @param int $applicantId
     * @return array
     */
    public function getApplicationsByApplicantId($applicantId)
    {
        return $this->where('applicant_id', $applicantId)->findAll();
    }

    /**
     * Get applications by position ID
     *
     * @param int $positionId
     * @return array
     */
    public function getApplicationsByPositionId($positionId)
    {
        return $this->where('position_id', $positionId)->findAll();
    }

    /**
     * Get application by application number
     *
     * @param string $applicationNumber
     * @return array|null
     */
    public function getApplicationByNumber($applicationNumber)
    {
        return $this->where('application_number', $applicationNumber)->first();
    }

    /**
     * Get applications by pre-screening status
     *
     * @param string $status
     * @return array
     */
    public function getApplicationsByPreScreeningStatus($status)
    {
        return $this->where('pre_screened_status', $status)->findAll();
    }

    /**
     * Get applications pending pre-screening (acknowledged but not pre-screened)
     *
     * @return array
     */
    public function getApplicationsPendingPreScreening()
    {
        return $this->where('recieved_acknowledged IS NOT NULL')
                    ->where('pre_screened IS NULL OR pre_screened = ""')
                    ->findAll();
    }

    /**
     * Get applications by profile status
     *
     * @param string $status
     * @return array
     */
    public function getApplicationsByProfileStatus($status)
    {
        return $this->where('profile_status', $status)->findAll();
    }

    /**
     * Get applications that have been profiled
     *
     * @return array
     */
    public function getProfiledApplications()
    {
        return $this->where('profiled_at IS NOT NULL')
                    ->where('profile_status IS NOT NULL')
                    ->findAll();
    }

    /**
     * Get applications that have been rated
     *
     * @return array
     */
    public function getRatedApplications()
    {
        return $this->where('rated_at IS NOT NULL')
                    ->where('rated', 'yes')
                    ->findAll();
    }

    /**
     * Get total rating score for an application
     *
     * @param int $applicationId
     * @return array
     */
    public function getApplicationRatingScore($applicationId)
    {
        $application = $this->find($applicationId);

        if (!$application) {
            return [
                'total_score' => 0,
                'max_score' => 0,
                'percentage' => 0,
                'details' => []
            ];
        }

        // Calculate scores from all rating categories
        $scores = [
            'age' => [
                'score' => $application['rating_age'] ?? 0,
                'max' => $application['rating_age_max'] ?? 0,
                'label' => 'Age'
            ],
            'qualification' => [
                'score' => $application['rating_qualification'] ?? 0,
                'max' => $application['rating_qualification_max'] ?? 0,
                'label' => 'Qualification'
            ],
            'experience_private_non_relevant' => [
                'score' => $application['rating_experience_private_non_relevant'] ?? 0,
                'max' => $application['rating_experience_private_non_relevant_max'] ?? 0,
                'label' => 'Private Sector Non-Relevant Experience'
            ],
            'experience_private_relevant' => [
                'score' => $application['rating_experience_private_relevant'] ?? 0,
                'max' => $application['rating_experience_private_relevant_max'] ?? 0,
                'label' => 'Private Sector Relevant Experience'
            ],
            'experience_public_non_relevant' => [
                'score' => $application['rating_experience_public_non_relevant'] ?? 0,
                'max' => $application['rating_experience_public_non_relevant_max'] ?? 0,
                'label' => 'Public Sector Non-Relevant Experience'
            ],
            'experience_public_relevant' => [
                'score' => $application['rating_experience_public_relevant'] ?? 0,
                'max' => $application['rating_experience_public_relevant_max'] ?? 0,
                'label' => 'Public Sector Relevant Experience'
            ],
            'trainings' => [
                'score' => $application['rating_trainings'] ?? 0,
                'max' => $application['rating_trainings_max'] ?? 0,
                'label' => 'Trainings'
            ],
            'skills_competencies' => [
                'score' => $application['rating_skills_competencies'] ?? 0,
                'max' => $application['rating_skills_competencies_max'] ?? 0,
                'label' => 'Skills & Competencies'
            ],
            'knowledge' => [
                'score' => $application['rating_knowledge'] ?? 0,
                'max' => $application['rating_knowledge_max'] ?? 0,
                'label' => 'Knowledge'
            ],
            'public_service' => [
                'score' => $application['rating_public_service'] ?? 0,
                'max' => $application['rating_public_service_max'] ?? 0,
                'label' => 'Public Service'
            ],
            'capability' => [
                'score' => $application['rating_capability'] ?? 0,
                'max' => $application['rating_capability_max'] ?? 0,
                'label' => 'Capability'
            ]
        ];

        // Calculate totals
        $totalScore = 0;
        $maxScore = 0;

        foreach ($scores as $category) {
            $totalScore += $category['score'];
            $maxScore += $category['max'];
        }

        $percentage = $maxScore > 0 ? round(($totalScore / $maxScore) * 100, 2) : 0;

        return [
            'total_score' => $totalScore,
            'max_score' => $maxScore,
            'percentage' => $percentage,
            'details' => $scores,
            'remarks' => $application['rating_remarks'] ?? ''
        ];
    }

    /**
     * Get applications by exercise ID
     *
     * @param int $exerciseId
     * @return array
     */
    public function getApplicationsByExerciseId($exerciseId)
    {
        return $this->where('exercise_id', $exerciseId)
                    ->orderBy('created_at', 'DESC')
                    ->findAll();
    }

    /**
     * Get applications by exercise ID where received_acknowledged is not null
     *
     * @param int $exerciseId
     * @return array
     */
    public function getAcknowledgedApplicationsByExerciseId($exerciseId)
    {
        return $this->select('appx_application_information.*, applicants.fname, applicants.lname, positions.designation as position_name')
                 ->join('applicants', 'applicants.applicant_id = appx_application_information.applicant_id', 'left')
                 ->join('positions', 'positions.id = appx_application_information.position_id', 'left')
                 ->join('positions_groups', 'positions_groups.id = positions.position_group_id', 'left')
                 ->join('exercises', 'exercises.id = positions_groups.exercise_id', 'left')
                 ->where('exercises.id', $exerciseId)
                 ->where('appx_application_information.recieved_acknowledged IS NOT NULL')
                 ->orderBy('appx_application_information.created_at', 'DESC')
                 ->findAll();
    }

    /**
     * Get applications by position ID that have been rated
     *
     * @param int $positionId
     * @return array
     */
    public function getRatedApplicationsByPositionId($positionId)
    {
        return $this->where('position_id', $positionId)
                    ->where('rated', 'yes')
                    ->where('rated_at IS NOT NULL')
                    ->findAll();
    }

    /**
     * Get applications by exercise ID that have been rated
     *
     * @param int $exerciseId
     * @return array
     */
    public function getRatedApplicationsByExerciseId($exerciseId)
    {
        return $this->where('exercise_id', $exerciseId)
                    ->where('rated', 'yes')
                    ->where('rated_at IS NOT NULL')
                    ->findAll();
    }

    /**
     * Get top rated applications by position ID
     *
     * @param int $positionId
     * @param int $limit Number of top applications to return
     * @return array
     */
    public function getTopRatedApplicationsByPositionId($positionId, $limit = 10)
    {
        $applications = $this->where('position_id', $positionId)
                            ->where('rated', 'yes')
                            ->where('rated_at IS NOT NULL')
                            ->findAll();

        // Calculate total scores for each application
        $scoredApplications = [];
        foreach ($applications as $application) {
            $totalScore = 0;
            $maxScore = 0;

            // Add up all rating fields
            $ratingFields = [
                'rating_age' => 'rating_age_max',
                'rating_qualification' => 'rating_qualification_max',
                'rating_experience_private_non_relevant' => 'rating_experience_private_non_relevant_max',
                'rating_experience_private_relevant' => 'rating_experience_private_relevant_max',
                'rating_experience_public_non_relevant' => 'rating_experience_public_non_relevant_max',
                'rating_experience_public_relevant' => 'rating_experience_public_relevant_max',
                'rating_trainings' => 'rating_trainings_max',
                'rating_skills_competencies' => 'rating_skills_competencies_max',
                'rating_knowledge' => 'rating_knowledge_max',
                'rating_public_service' => 'rating_public_service_max',
                'rating_capability' => 'rating_capability_max'
            ];

            foreach ($ratingFields as $scoreField => $maxField) {
                $totalScore += (int)($application[$scoreField] ?? 0);
                $maxScore += (int)($application[$maxField] ?? 0);
            }

            $percentage = $maxScore > 0 ? ($totalScore / $maxScore) * 100 : 0;

            $application['total_score'] = $totalScore;
            $application['max_score'] = $maxScore;
            $application['score_percentage'] = $percentage;

            $scoredApplications[] = $application;
        }

        // Sort by total score (descending)
        usort($scoredApplications, function($a, $b) {
            return $b['total_score'] - $a['total_score'];
        });

        // Return only the requested number of applications
        return array_slice($scoredApplications, 0, $limit);
    }
}